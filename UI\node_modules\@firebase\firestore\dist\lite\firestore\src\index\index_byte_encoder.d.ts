/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { IndexKind } from '../model/field_index';
import { DirectionalIndexByteEncoder } from './directional_index_byte_encoder';
/**
 * Implements `DirectionalIndexByteEncoder` using `OrderedCodeWriter` for the
 * actual encoding.
 */
export declare class IndexByteEncoder {
    private orderedCode;
    private ascending;
    private descending;
    seed(encodedBytes: Uint8Array): void;
    forKind(kind: IndexKind): DirectionalIndexByteEncoder;
    encodedBytes(): Uint8Array;
    reset(): void;
}
