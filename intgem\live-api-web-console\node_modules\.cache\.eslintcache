[{"C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\index.tsx": "1", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\reportWebVitals.ts": "2", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\App.tsx": "3", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\contexts\\LiveAPIContext.tsx": "4", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\control-tray\\ControlTray.tsx": "5", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\interview-questions\\InterviewQuestions.tsx": "6", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\interview-avatar\\InterviewAvatar.tsx": "7", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\get-started\\GetStarted.tsx": "8", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\hooks\\use-live-api.ts": "9", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\hooks\\use-webcam.ts": "10", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\audio-recorder.ts": "11", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\audio-pulse\\AudioPulse.tsx": "12", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\transcription-display\\TranscriptionDisplay.tsx": "13", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\audio-streamer.ts": "14", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\multimodal-live-client.ts": "15", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\stt-client.ts": "16", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\utils.ts": "17", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\worklets\\vol-meter.ts": "18", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\audioworklet-registry.ts": "19", "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\worklets\\audio-processing.ts": "20"}, {"size": 1185, "mtime": 1747466320000, "results": "21", "hashOfConfig": "22"}, {"size": 1052, "mtime": 1747466320000, "results": "23", "hashOfConfig": "22"}, {"size": 3866, "mtime": 1758255487156, "results": "24", "hashOfConfig": "22"}, {"size": 1429, "mtime": 1747466320000, "results": "25", "hashOfConfig": "22"}, {"size": 6691, "mtime": 1747544886000, "results": "26", "hashOfConfig": "22"}, {"size": 2071, "mtime": 1758194064921, "results": "27", "hashOfConfig": "22"}, {"size": 4744, "mtime": 1758198835724, "results": "28", "hashOfConfig": "22"}, {"size": 1958, "mtime": 1747544706000, "results": "29", "hashOfConfig": "22"}, {"size": 5361, "mtime": 1758194064920, "results": "30", "hashOfConfig": "22"}, {"size": 1910, "mtime": 1747466320000, "results": "31", "hashOfConfig": "22"}, {"size": 3999, "mtime": 1747551154000, "results": "32", "hashOfConfig": "22"}, {"size": 1754, "mtime": 1747466320000, "results": "33", "hashOfConfig": "22"}, {"size": 1920, "mtime": 1747550315000, "results": "34", "hashOfConfig": "22"}, {"size": 8571, "mtime": 1747466320000, "results": "35", "hashOfConfig": "22"}, {"size": 20760, "mtime": 1747551093000, "results": "36", "hashOfConfig": "22"}, {"size": 7163, "mtime": 1758194064918, "results": "37", "hashOfConfig": "22"}, {"size": 2543, "mtime": 1747466320000, "results": "38", "hashOfConfig": "22"}, {"size": 1845, "mtime": 1747466320000, "results": "39", "hashOfConfig": "22"}, {"size": 1301, "mtime": 1747466320000, "results": "40", "hashOfConfig": "22"}, {"size": 2052, "mtime": 1747466320000, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xqtk9p", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\index.tsx", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\reportWebVitals.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\App.tsx", ["102"], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\contexts\\LiveAPIContext.tsx", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\control-tray\\ControlTray.tsx", [], ["103"], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\interview-questions\\InterviewQuestions.tsx", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\interview-avatar\\InterviewAvatar.tsx", ["104"], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\get-started\\GetStarted.tsx", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\hooks\\use-live-api.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\hooks\\use-webcam.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\audio-recorder.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\audio-pulse\\AudioPulse.tsx", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\components\\transcription-display\\TranscriptionDisplay.tsx", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\audio-streamer.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\multimodal-live-client.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\stt-client.ts", ["105"], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\utils.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\worklets\\vol-meter.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\audioworklet-registry.ts", [], [], "C:\\PiyushWorkspace\\code\\HackvYuha\\intgem\\live-api-web-console\\src\\lib\\worklets\\audio-processing.ts", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "106", "line": 10, "column": 0, "nodeType": null}, {"ruleId": "107", "severity": 1, "message": "108", "line": 153, "column": 6, "nodeType": "109", "endLine": 153, "endColumn": 8, "suggestions": "110", "suppressions": "111"}, {"ruleId": "112", "severity": 1, "message": "113", "line": 14, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 14, "endColumn": 20}, {"ruleId": "112", "severity": 1, "message": "116", "line": 4, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 4, "endColumn": 17}, "Parsing error: Merge conflict marker encountered.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'activeVideoStream', 'changeStreams', 'supportsVideo', and 'webcam'. Either include them or remove the dependency array.", "ArrayExpression", ["117"], ["118"], "@typescript-eslint/no-unused-vars", "'mouthState' is assigned a value but never used.", "Identifier", "unusedVar", "'Content' is defined but never used.", {"desc": "119", "fix": "120"}, {"kind": "121", "justification": "122"}, "Update the dependencies array to be: [activeVideoStream, changeStreams, supportsVideo, webcam]", {"range": "123", "text": "124"}, "directive", "", [4706, 4708], "[activeVideoStream, changeStreams, supportsVideo, webcam]"]